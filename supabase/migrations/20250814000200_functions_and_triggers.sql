-- RatioHub Functions and Triggers
-- This migration creates all functions, triggers, and utility functions

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Function to update updated_at column
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION public.is_admin(user_uuid uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.user_roles 
        WHERE user_id = user_uuid AND role = 'admin'
    );
END;
$$;

-- Function to check if user is project manager
CREATE OR REPLACE FUNCTION public.is_project_manager(user_uuid uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.user_roles 
        WHERE user_id = user_uuid AND role IN ('admin', 'project_manager')
    );
END;
$$;

-- Function to check if user has access to project
CREATE OR REPLACE FUNCTION public.has_project_access(user_uuid uuid, proj_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.projects p
        LEFT JOIN public.team_members tm ON tm.team_id = p.team_id
        WHERE p.id = proj_id 
        AND (
            p.created_by = user_uuid 
            OR p.client_id = user_uuid 
            OR tm.user_id = user_uuid
            OR is_admin(user_uuid)
            OR is_project_manager(user_uuid)
        )
    );
END;
$$;

-- ============================================================================
-- ID GENERATION FUNCTIONS
-- ============================================================================

-- Project ID Generation Function
CREATE OR REPLACE FUNCTION public.generate_project_id(project_name text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    prefix TEXT;
    next_number INTEGER;
    new_id TEXT;
BEGIN
    -- Extract first two letters from project name
    prefix := upper(substring(regexp_replace(project_name, '[^a-zA-Z]', '', 'g'), 1, 2));
    
    -- If less than 2 letters, pad with 'X'
    IF length(prefix) < 2 THEN
        prefix := rpad(prefix, 2, 'X');
    END IF;
    
    -- Find highest existing number for this prefix
    SELECT COALESCE(MAX(CAST(substring(project_id, 3) AS INTEGER)), 0) + 1
    INTO next_number
    FROM public.projects
    WHERE project_id ~ ('^' || prefix || '[0-9]{3}$');
    
    -- Generate new project ID
    new_id := prefix || lpad(next_number::TEXT, 3, '0');
    
    RETURN new_id;
END;
$$;

-- Task ID Generation Function
CREATE OR REPLACE FUNCTION public.generate_task_id(proj_id uuid)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    project_code TEXT;
    next_number INTEGER;
    new_id TEXT;
BEGIN
    -- Get project code
    SELECT project_id INTO project_code
    FROM public.projects
    WHERE id = proj_id;
    
    -- Find highest existing task number for this project
    SELECT COALESCE(MAX(CAST(substring(task_id, length(project_code) + 3) AS INTEGER)), 0) + 1
    INTO next_number
    FROM public.tasks
    WHERE task_id ~ ('^' || project_code || '-T[0-9]{3}$');
    
    -- Generate new task ID
    new_id := project_code || '-T' || lpad(next_number::TEXT, 3, '0');
    
    RETURN new_id;
END;
$$;

-- ============================================================================
-- TRIGGER FUNCTIONS
-- ============================================================================

-- Function to set project ID on insert
CREATE OR REPLACE FUNCTION public.set_project_id()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    IF NEW.project_id IS NULL OR NEW.project_id = '' THEN
        NEW.project_id := public.generate_project_id(NEW.name);
    END IF;
    RETURN NEW;
END;
$$;

-- Function to set task ID on insert
CREATE OR REPLACE FUNCTION public.set_task_id()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    IF NEW.task_id IS NULL OR NEW.task_id = '' THEN
        NEW.task_id := public.generate_task_id(NEW.project_id);
    END IF;
    RETURN NEW;
END;
$$;

-- Function to handle profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    INSERT INTO public.profiles (user_id, email, first_name, last_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'last_name', '')
    );
    RETURN NEW;
END;
$$;

-- ============================================================================
-- TRIGGERS
-- ============================================================================

-- Updated at triggers
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON public.profiles 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_teams_updated_at 
    BEFORE UPDATE ON public.teams 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_projects_updated_at 
    BEFORE UPDATE ON public.projects 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_phases_updated_at 
    BEFORE UPDATE ON public.phases 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_milestones_updated_at 
    BEFORE UPDATE ON public.milestones 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at 
    BEFORE UPDATE ON public.tasks 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_task_comments_updated_at 
    BEFORE UPDATE ON public.task_comments 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_time_entries_updated_at 
    BEFORE UPDATE ON public.time_entries 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_support_tickets_updated_at 
    BEFORE UPDATE ON public.support_tickets 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- ID generation triggers
CREATE TRIGGER set_project_id_trigger 
    BEFORE INSERT ON public.projects 
    FOR EACH ROW EXECUTE FUNCTION public.set_project_id();

CREATE TRIGGER set_task_id_trigger 
    BEFORE INSERT ON public.tasks 
    FOR EACH ROW EXECUTE FUNCTION public.set_task_id();

-- Profile creation trigger
CREATE TRIGGER on_auth_user_created 
    AFTER INSERT ON auth.users 
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
