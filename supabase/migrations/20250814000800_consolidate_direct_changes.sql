-- RatioHub Consolidation Migration
-- This migration consolidates changes that were made directly to the database
-- instead of through proper migration scripts

-- ============================================================================
-- FUNCTIONS THAT WERE CREATED DIRECTLY
-- ============================================================================

-- Function to generate project IDs (format: AB123)
CREATE OR REPLACE FUNCTION public.generate_project_id()
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    new_id text;
    counter integer;
    prefix text;
BEGIN
    -- Get the highest existing project number
    SELECT COALESCE(MAX(CAST(substring(project_id, 3) AS integer)), 0) + 1
    INTO counter
    FROM public.projects
    WHERE project_id ~ '^[A-Z]{2}[0-9]{3}$';
    
    -- Generate two random uppercase letters
    prefix := chr(65 + floor(random() * 26)::int) || chr(65 + floor(random() * 26)::int);
    
    -- Ensure uniqueness by checking if combination exists
    LOOP
        new_id := prefix || lpad(counter::text, 3, '0');
        
        -- Check if this ID already exists
        IF NOT EXISTS (SELECT 1 FROM public.projects WHERE project_id = new_id) THEN
            EXIT;
        END IF;
        
        -- If exists, increment counter and try again
        counter := counter + 1;
        
        -- If counter gets too high, generate new prefix
        IF counter > 999 THEN
            prefix := chr(65 + floor(random() * 26)::int) || chr(65 + floor(random() * 26)::int);
            counter := 1;
        END IF;
    END LOOP;
    
    RETURN new_id;
END;
$$;

-- Function to set project ID on insert
CREATE OR REPLACE FUNCTION public.set_project_id()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    IF NEW.project_id IS NULL OR NEW.project_id = '' THEN
        NEW.project_id := public.generate_project_id();
    END IF;
    RETURN NEW;
END;
$$;

-- Function to generate task IDs (format: AB123-T001)
CREATE OR REPLACE FUNCTION public.generate_task_id(p_project_id text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    task_counter integer;
    new_task_id text;
BEGIN
    -- Get the highest task number for this project
    SELECT COALESCE(MAX(CAST(substring(task_id, length(p_project_id) + 3) AS integer)), 0) + 1
    INTO task_counter
    FROM public.tasks
    WHERE task_id LIKE p_project_id || '-T%'
    AND task_id ~ ('^' || p_project_id || '-T[0-9]{3}$');
    
    -- Generate new task ID
    new_task_id := p_project_id || '-T' || lpad(task_counter::text, 3, '0');
    
    RETURN new_task_id;
END;
$$;

-- Function to set task ID on insert
CREATE OR REPLACE FUNCTION public.set_task_id()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    project_code text;
BEGIN
    IF NEW.task_id IS NULL OR NEW.task_id = '' THEN
        -- Get project code from project_id
        SELECT project_id INTO project_code
        FROM public.projects
        WHERE id = NEW.project_id;
        
        IF project_code IS NOT NULL THEN
            NEW.task_id := public.generate_task_id(project_code);
        END IF;
    END IF;
    RETURN NEW;
END;
$$;

-- Function to auto-update task time tracking
CREATE OR REPLACE FUNCTION public.auto_update_task_time()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    total_minutes integer;
    total_hours numeric;
BEGIN
    -- Calculate total time for the task
    SELECT COALESCE(SUM(duration_minutes), 0)
    INTO total_minutes
    FROM public.time_entries
    WHERE task_id = COALESCE(NEW.task_id, OLD.task_id)
    AND end_time IS NOT NULL;
    
    -- Convert to hours
    total_hours := total_minutes::numeric / 60;
    
    -- Update task actual hours
    UPDATE public.tasks
    SET actual_hours = total_hours
    WHERE id = COALESCE(NEW.task_id, OLD.task_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$;

-- Function to log project activity
CREATE OR REPLACE FUNCTION public.log_project_activity()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    activity_type text;
    entity_type text;
    entity_id uuid;
    project_uuid uuid;
BEGIN
    -- Determine activity type based on operation
    CASE TG_OP
        WHEN 'INSERT' THEN activity_type := 'created';
        WHEN 'UPDATE' THEN activity_type := 'updated';
        WHEN 'DELETE' THEN activity_type := 'deleted';
    END CASE;
    
    -- Determine entity type and IDs based on table
    CASE TG_TABLE_NAME
        WHEN 'tasks' THEN
            entity_type := 'task';
            entity_id := COALESCE(NEW.id, OLD.id);
            project_uuid := COALESCE(NEW.project_id, OLD.project_id);
        WHEN 'milestones' THEN
            entity_type := 'milestone';
            entity_id := COALESCE(NEW.id, OLD.id);
            project_uuid := COALESCE(NEW.project_id, OLD.project_id);
        WHEN 'projects' THEN
            entity_type := 'project';
            entity_id := COALESCE(NEW.id, OLD.id);
            project_uuid := entity_id;
        ELSE
            RETURN COALESCE(NEW, OLD);
    END CASE;
    
    -- Insert activity log
    INSERT INTO public.project_activity_logs (
        project_id,
        entity_type,
        entity_id,
        activity_type,
        description,
        user_id
    ) VALUES (
        project_uuid,
        entity_type,
        entity_id,
        activity_type,
        activity_type || ' ' || entity_type,
        auth.uid()
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$;

-- ============================================================================
-- TRIGGERS THAT WERE CREATED DIRECTLY
-- ============================================================================

-- Drop existing triggers if they exist (to avoid conflicts)
DROP TRIGGER IF EXISTS set_project_id_trigger ON public.projects;
DROP TRIGGER IF EXISTS set_task_id_trigger ON public.tasks;
DROP TRIGGER IF EXISTS auto_update_task_time_trigger ON public.time_entries;
DROP TRIGGER IF EXISTS log_project_activity_tasks_trigger ON public.tasks;
DROP TRIGGER IF EXISTS log_project_activity_milestones_trigger ON public.milestones;
DROP TRIGGER IF EXISTS log_project_activity_projects_trigger ON public.projects;

-- Create triggers
CREATE TRIGGER set_project_id_trigger
    BEFORE INSERT ON public.projects
    FOR EACH ROW EXECUTE FUNCTION public.set_project_id();

CREATE TRIGGER set_task_id_trigger
    BEFORE INSERT ON public.tasks
    FOR EACH ROW EXECUTE FUNCTION public.set_task_id();

CREATE TRIGGER auto_update_task_time_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.time_entries
    FOR EACH ROW EXECUTE FUNCTION public.auto_update_task_time();

CREATE TRIGGER log_project_activity_tasks_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.tasks
    FOR EACH ROW EXECUTE FUNCTION public.log_project_activity();

CREATE TRIGGER log_project_activity_milestones_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.milestones
    FOR EACH ROW EXECUTE FUNCTION public.log_project_activity();

CREATE TRIGGER log_project_activity_projects_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION public.log_project_activity();

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON FUNCTION public.generate_project_id() IS 'Generates unique project IDs in format AB123';
COMMENT ON FUNCTION public.generate_task_id(text) IS 'Generates unique task IDs in format AB123-T001';
COMMENT ON FUNCTION public.set_project_id() IS 'Trigger function to auto-generate project IDs';
COMMENT ON FUNCTION public.set_task_id() IS 'Trigger function to auto-generate task IDs';
COMMENT ON FUNCTION public.auto_update_task_time() IS 'Trigger function to update task actual hours from time entries';
COMMENT ON FUNCTION public.log_project_activity() IS 'Trigger function to log project-related activities';
