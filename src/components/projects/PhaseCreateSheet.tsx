import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Textarea } from '@/components/ui/textarea';
import { supabase } from '@/integrations/supabase/client';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { CalendarIcon, Loader2 } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

const phaseSchema = z.object({
  name: z.string().min(1, 'Phase name is required'),
  description: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  status: z.enum(['planning', 'in_progress', 'completed', 'on_hold']),
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return data.endDate >= data.startDate;
  }
  return true;
}, {
  message: "End date must be after start date",
  path: ["endDate"],
});

type PhaseFormData = z.infer<typeof phaseSchema>;

interface PhaseCreateSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  onPhaseCreated?: () => void;
  editingPhase?: any; // Phase data for editing mode
}

export const PhaseCreateSheet: React.FC<PhaseCreateSheetProps> = ({
  open,
  onOpenChange,
  projectId,
  onPhaseCreated,
  editingPhase,
}) => {
  const queryClient = useQueryClient();

  const form = useForm<PhaseFormData>({
    resolver: zodResolver(phaseSchema),
    defaultValues: {
      name: '',
      description: '',
      status: 'planning',
    },
  });

  // Populate form when editing
  useEffect(() => {
    if (editingPhase && open) {
      form.reset({
        name: editingPhase.name || '',
        description: editingPhase.description || '',
        startDate: editingPhase.start_date ? new Date(editingPhase.start_date) : undefined,
        endDate: editingPhase.end_date ? new Date(editingPhase.end_date) : undefined,
        status: editingPhase.status || 'planning',
      });
    } else if (!editingPhase && open) {
      // Reset to default values when creating new phase
      form.reset({
        name: '',
        description: '',
        status: 'planning',
      });
    }
  }, [editingPhase, open, form]);

  const phaseMutation = useMutation({
    mutationFn: async (data: PhaseFormData) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const phaseData = {
        name: data.name,
        description: data.description,
        start_date: data.startDate ? format(data.startDate, 'yyyy-MM-dd') : null,
        end_date: data.endDate ? format(data.endDate, 'yyyy-MM-dd') : null,
        status: data.status,
        ...(editingPhase ? { updated_at: new Date().toISOString() } : { project_id: projectId, order_index: 0, created_by: user.id }),
      };

      if (editingPhase) {
        // Update existing phase
        const { error } = await supabase
          .from('phases')
          .update(phaseData)
          .eq('id', editingPhase.id);

        if (error) throw error;
      } else {
        // Create new phase
        const { error } = await supabase
          .from('phases')
          .insert(phaseData);

        if (error) throw error;
      }
    },
    onSuccess: () => {
      toast.success(editingPhase ? 'Phase updated successfully!' : 'Phase created successfully!');
      if (!editingPhase) {
        form.reset();
      }
      onOpenChange(false);
      queryClient.invalidateQueries({ queryKey: ['project-phases', projectId] });
      onPhaseCreated?.();
    },
    onError: (error: any) => {
      toast.error(error.message || (editingPhase ? 'Failed to update phase' : 'Failed to create phase'));
    },
  });

  const onSubmit = (data: PhaseFormData) => {
    phaseMutation.mutate(data);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-md">
        <SheetHeader>
          <SheetTitle>{editingPhase ? 'Edit Phase' : 'Create New Phase'}</SheetTitle>
          <SheetDescription>
            {editingPhase
              ? 'Update phase details and timeline.'
              : 'Add a new phase to organize your project milestones.'
            }
          </SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 mt-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phase Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter phase name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe what this phase involves..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="planning">Planning</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="on_hold">On Hold</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Start Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={phaseMutation.isPending}
                className="flex-1"
              >
                {phaseMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {editingPhase ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  editingPhase ? 'Update Phase' : 'Create Phase'
                )}
              </Button>
            </div>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};