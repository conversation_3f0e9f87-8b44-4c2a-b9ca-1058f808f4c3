import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import {
    Calendar,
    DollarSign,
    Edit,
    ExternalLink,
    Folder,
    MoreHorizontal,
    Users
} from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';
import { ProjectCreateSheet } from './ProjectCreateSheet';

interface Project {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  status: string;
  start_date?: string;
  end_date?: string;
  budget?: number;
  currency?: string;
  is_billable: boolean;
  created_at: string;
  profiles?: {
    first_name?: string;
    last_name?: string;
    email: string;
  };
  teams?: {
    name: string;
  };
  _count_tasks?: { count: number }[];
  _count_milestones?: { count: number }[];
}

interface ProjectsListProps {
  projects: Project[];
  isLoading: boolean;
  onRefresh: () => void;
}

export const ProjectsList: React.FC<ProjectsListProps> = ({
  projects,
  isLoading,
  onRefresh
}) => {
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planning': return 'bg-blue-500/10 text-blue-600 border-blue-200';
      case 'active': return 'bg-green-500/10 text-green-600 border-green-200';
      case 'on_hold': return 'bg-yellow-500/10 text-yellow-600 border-yellow-200';
      case 'completed': return 'bg-emerald-500/10 text-emerald-600 border-emerald-200';
      case 'cancelled': return 'bg-red-500/10 text-red-600 border-red-200';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const calculateProgress = (project: Project) => {
    // Mock progress calculation - in real app, this would be based on completed tasks
    const totalTasks = project._count_tasks?.[0]?.count || 0;
    if (totalTasks === 0) return 0;
    
    // Mock: assume 60% completion for active projects
    switch (project.status) {
      case 'completed': return 100;
      case 'active': return Math.min(60 + Math.random() * 30, 95);
      case 'planning': return Math.random() * 20;
      default: return 0;
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="space-y-2">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-2 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <Card className="p-12 text-center">
        <Folder className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No projects found</h3>
        <p className="text-muted-foreground mb-4">
          Get started by creating your first project or adjust your search filters.
        </p>
        <Button onClick={onRefresh} variant="outline">
          Refresh
        </Button>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {projects.map((project) => {
        const progress = calculateProgress(project);
        
        return (
          <Card key={project.id} className="group hover:shadow-md transition-shadow">
            <CardHeader className="space-y-3">
              <div className="flex items-start justify-between">
                <div className="space-y-1 flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors">
                      {project.name}
                    </h3>
                    <Badge variant="outline" className={`text-xs ${getStatusColor(project.status)}`}>
                      {project.status?.replace('_', ' ').toUpperCase()}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground font-mono">
                    {project.project_id}
                  </p>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link to={`/projects/${project.id}`} className="flex items-center gap-2">
                        <ExternalLink className="w-4 h-4" />
                        View Details
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setEditingProject(project)}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Project
                    </DropdownMenuItem>
                    <DropdownMenuItem>Archive</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {project.description && (
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {project.description}
                </p>
              )}
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Progress */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Progress</span>
                  <span className="font-medium">{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              {/* Project Info */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  {project.start_date && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(project.start_date).toLocaleDateString()}</span>
                    </div>
                  )}
                  
                  {project.budget && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <DollarSign className="w-4 h-4" />
                      <span>${Number(project.budget).toLocaleString()}</span>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  {project.teams && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Users className="w-4 h-4" />
                      <span className="truncate">{project.teams.name}</span>
                    </div>
                  )}

                  {project.profiles && (
                    <div className="text-xs text-muted-foreground">
                      {project.profiles.first_name} {project.profiles.last_name}
                    </div>
                  )}
                </div>
              </div>

              {/* Quick Stats */}
              <div className="flex justify-between pt-2 border-t text-xs text-muted-foreground">
                <span>
                  {project._count_tasks?.[0]?.count || 0} tasks
                </span>
                <span>
                  {project._count_milestones?.[0]?.count || 0} milestones
                </span>
                <span>
                  {project.is_billable ? 'Billable' : 'Fixed'}
                </span>
              </div>

              {/* Action Button */}
              <div className="pt-2">
                <Button asChild variant="outline" size="sm" className="w-full">
                  <Link to={`/projects/${project.id}`}>
                    View Project
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>

    {/* Project Edit Sheet */}
    {editingProject && (
      <ProjectCreateSheet
        open={!!editingProject}
        onOpenChange={(open) => !open && setEditingProject(null)}
        onProjectCreated={() => {
          onRefresh();
          setEditingProject(null);
        }}
        editingProject={editingProject}
      />
    )}
  );
};