import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
    CheckCircle,
    Clock,
    Database,
    FileText,
    Key,
    Shield,
    User
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';



interface PasswordPolicy {
  min_length: number;
  require_uppercase: boolean;
  require_lowercase: boolean;
  require_numbers: boolean;
  require_symbols: boolean;
  max_age_days: number;
}

interface SecuritySettings {
  two_factor_required: boolean;
  session_timeout_hours: number;
  password_policy: PasswordPolicy;
  login_attempts_limit: number;
  account_lockout_duration: number;
}

export const SecurityManager = () => {
  const queryClient = useQueryClient();
  const [settings, setSettings] = useState<SecuritySettings>({
    two_factor_required: false,
    session_timeout_hours: 8,
    password_policy: {
      min_length: 8,
      require_uppercase: true,
      require_lowercase: true,
      require_numbers: true,
      require_symbols: false,
      max_age_days: 90
    },
    login_attempts_limit: 5,
    account_lockout_duration: 30
  });



  const { data: systemHealth } = useQuery({
    queryKey: ['system-health'],
    queryFn: async () => {
      return {
        database_status: 'healthy',
        auth_service: 'healthy',
        file_storage: 'healthy',
        backup_status: 'completed',
        last_backup: new Date(Date.now() - ********).toISOString(),
        uptime_percentage: 99.9,
        active_users: 42,
        total_projects: 18,
        storage_used_gb: 127.5,
        storage_limit_gb: 500
      };
    }
  });

  const updateSecurityMutation = useMutation({
    mutationFn: async (newSettings: SecuritySettings) => {
      // Mock settings update
      await new Promise(resolve => setTimeout(resolve, 1000));
      return newSettings;
    },
    onSuccess: (newSettings) => {
      setSettings(newSettings);
      toast.success('Security settings updated successfully');
    },
    onError: () => {
      toast.error('Failed to update security settings');
    }
  });

  const runBackupMutation = useMutation({
    mutationFn: async () => {
      await new Promise(resolve => setTimeout(resolve, 3000));
      return { success: true, backup_id: `backup_${Date.now()}` };
    },
    onSuccess: () => {
      toast.success('Manual backup completed successfully');
      queryClient.invalidateQueries({ queryKey: ['system-health'] });
    },
    onError: () => {
      toast.error('Backup failed');
    }
  });



  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Security & System Management</h2>
        <Badge variant="outline">Admin Only</Badge>
      </div>

      <Tabs defaultValue="security" className="space-y-4">
        <TabsList>
          <TabsTrigger value="security">Security Settings</TabsTrigger>
          <TabsTrigger value="system">System Health</TabsTrigger>
          <TabsTrigger value="backup">Backup & Recovery</TabsTrigger>
        </TabsList>

        <TabsContent value="security" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Authentication Security
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Require Two-Factor Authentication</p>
                    <p className="text-sm text-muted-foreground">Enforce 2FA for all users</p>
                  </div>
                  <Switch
                    checked={settings.two_factor_required}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({ ...prev, two_factor_required: checked }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label>Session Timeout (hours)</Label>
                  <Select 
                    value={settings.session_timeout_hours.toString()} 
                    onValueChange={(value) => 
                      setSettings(prev => ({ ...prev, session_timeout_hours: parseInt(value) }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 hour</SelectItem>
                      <SelectItem value="4">4 hours</SelectItem>
                      <SelectItem value="8">8 hours</SelectItem>
                      <SelectItem value="24">24 hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Login Attempts Limit</Label>
                  <Input
                    type="number"
                    value={settings.login_attempts_limit}
                    onChange={(e) =>
                      setSettings(prev => ({ ...prev, login_attempts_limit: parseInt(e.target.value) }))
                    }
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  Password Policy
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Minimum Length</Label>
                  <Input
                    type="number"
                    value={settings.password_policy.min_length}
                    onChange={(e) =>
                      setSettings(prev => ({
                        ...prev,
                        password_policy: {
                          ...prev.password_policy,
                          min_length: parseInt(e.target.value)
                        }
                      }))
                    }
                  />
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Require uppercase letters</span>
                    <Switch
                      checked={settings.password_policy.require_uppercase}
                      onCheckedChange={(checked) =>
                        setSettings(prev => ({
                          ...prev,
                          password_policy: { ...prev.password_policy, require_uppercase: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Require lowercase letters</span>
                    <Switch
                      checked={settings.password_policy.require_lowercase}
                      onCheckedChange={(checked) =>
                        setSettings(prev => ({
                          ...prev,
                          password_policy: { ...prev.password_policy, require_lowercase: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Require numbers</span>
                    <Switch
                      checked={settings.password_policy.require_numbers}
                      onCheckedChange={(checked) =>
                        setSettings(prev => ({
                          ...prev,
                          password_policy: { ...prev.password_policy, require_numbers: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Require symbols</span>
                    <Switch
                      checked={settings.password_policy.require_symbols}
                      onCheckedChange={(checked) =>
                        setSettings(prev => ({
                          ...prev,
                          password_policy: { ...prev.password_policy, require_symbols: checked }
                        }))
                      }
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-end">
            <Button 
              onClick={() => updateSecurityMutation.mutate(settings)}
              disabled={updateSecurityMutation.isPending}
            >
              {updateSecurityMutation.isPending ? 'Saving...' : 'Save Security Settings'}
            </Button>
          </div>
        </TabsContent>



        <TabsContent value="system" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Database className="h-5 w-5" />
                  Database
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Healthy</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <User className="h-5 w-5" />
                  Active Users
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemHealth?.active_users}</div>
                <p className="text-xs text-muted-foreground">Currently online</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <FileText className="h-5 w-5" />
                  Storage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-sm">
                    {systemHealth?.storage_used_gb}GB / {systemHealth?.storage_limit_gb}GB
                  </div>
                  <Progress 
                    value={((systemHealth?.storage_used_gb || 0) / (systemHealth?.storage_limit_gb || 1)) * 100} 
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Clock className="h-5 w-5" />
                  Uptime
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemHealth?.uptime_percentage}%</div>
                <p className="text-xs text-muted-foreground">Last 30 days</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="backup" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Backup Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Automatic backups</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Last backup</p>
                  <p className="text-sm text-muted-foreground">
                    {systemHealth?.last_backup 
                      ? new Date(systemHealth.last_backup).toLocaleString()
                      : 'Never'
                    }
                  </p>
                </div>
                <Button 
                  onClick={() => runBackupMutation.mutate()}
                  disabled={runBackupMutation.isPending}
                  className="w-full"
                >
                  {runBackupMutation.isPending ? 'Creating Backup...' : 'Run Manual Backup'}
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Backup Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Backup Frequency</Label>
                  <Select defaultValue="daily">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">Every hour</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Retention Period</Label>
                  <Select defaultValue="30">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7">7 days</SelectItem>
                      <SelectItem value="30">30 days</SelectItem>
                      <SelectItem value="90">90 days</SelectItem>
                      <SelectItem value="365">1 year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button variant="outline" className="w-full">
                  Save Backup Settings
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};