import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';

interface SubtaskCreateSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  parentTaskId: string;
  projectId: string;
}

interface SubtaskFormData {
  title: string;
  description?: string;
  assigned_to?: string;
  due_date?: Date;
  status: 'todo' | 'in_progress' | 'review' | 'completed' | 'blocked';
}

export const SubtaskCreateSheet: React.FC<SubtaskCreateSheetProps> = ({
  open,
  onOpenChange,
  parentTaskId,
  projectId
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<SubtaskFormData>({
    defaultValues: {
      title: '',
      description: '',
      status: 'todo' as const,
    },
  });

  // Fetch team members for assignment
  const { data: teamMembers } = useQuery({
    queryKey: ['projectTeamMembers', projectId],
    queryFn: async () => {
      const { data: project } = await supabase
        .from('projects')
        .select('team_id')
        .eq('id', projectId)
        .single();

      if (!project?.team_id) return [];

      const { data: members } = await supabase
        .from('team_members')
        .select('user_id')
        .eq('team_id', project.team_id);

      if (!members || members.length === 0) return [];

      const userIds = members.map(m => m.user_id);
      const { data: profiles } = await supabase
        .from('profiles')
        .select('user_id, first_name, last_name, email')
        .in('user_id', userIds)
        .eq('is_active', true);

      return profiles || [];
    },
    enabled: open,
  });

  const createSubtaskMutation = useMutation({
    mutationFn: async (data: SubtaskFormData) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // For now, create subtasks as regular tasks with parent_task_id
      const subtaskData = {
        parent_task_id: parentTaskId,
        project_id: projectId,
        title: data.title,
        description: data.description || null,
        assigned_to: (data.assigned_to && data.assigned_to !== 'unassigned') ? data.assigned_to : null,
        due_date: data.due_date?.toISOString() || null,
        status: data.status,
        created_by: user.id,
        task_id: '', // Will be generated by trigger
      };

      const { error } = await supabase
        .from('tasks')
        .insert(subtaskData);

      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: 'Subtask created',
        description: 'The subtask has been created successfully.',
      });
      form.reset();
      onOpenChange(false);
      queryClient.invalidateQueries({ queryKey: ['projectTasks', projectId] });
      queryClient.invalidateQueries({ queryKey: ['task', parentTaskId] });
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating subtask',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (data: SubtaskFormData) => {
    createSubtaskMutation.mutate(data);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:max-w-lg">
        <SheetHeader>
          <SheetTitle>Create Subtask</SheetTitle>
        </SheetHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 mt-6">
            <FormField
              control={form.control}
              name="title"
              rules={{ required: 'Title is required' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter subtask title..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter subtask description..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {(['todo', 'in_progress', 'review', 'completed', 'blocked'] as const).map((status) => (
                        <SelectItem key={status} value={status}>
                          {status.replace('_', ' ').toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="assigned_to"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Assign To</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select team member" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="unassigned">Unassigned</SelectItem>
                      {teamMembers?.map((member) => (
                        <SelectItem key={member.user_id} value={member.user_id}>
                          {member.first_name} {member.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="due_date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Due Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                        >
                          {field.value ? (
                            format(field.value, 'PPP')
                          ) : (
                            <span className="text-muted-foreground">Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createSubtaskMutation.isPending}
                className="flex-1"
              >
                {createSubtaskMutation.isPending ? 'Creating...' : 'Create Subtask'}
              </Button>
            </div>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};