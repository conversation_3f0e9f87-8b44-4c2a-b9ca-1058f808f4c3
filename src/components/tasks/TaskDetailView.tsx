import { Ava<PERSON>, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/integrations/supabase/client';
import { cn } from '@/lib/utils';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
    Calendar,
    CheckCircle2,
    ListTodo,
    MessageCircle,
    Plus,
    Target,
    Timer,
    User
} from 'lucide-react';
import React, { useState } from 'react';
import { SubtaskCreateSheet } from './SubtaskCreateSheet';
import { TaskComments } from './TaskComments';

interface TaskDetailViewProps {
  taskId: string;
  onClose?: () => void;
}

export const TaskDetailView: React.FC<TaskDetailViewProps> = ({ taskId, onClose }) => {
  const [showComments, setShowComments] = useState(false);
  const [isSubtaskSheetOpen, setIsSubtaskSheetOpen] = useState(false);



  // Fetch task details with manual joins
  const { data: task, isLoading, refetch, error } = useQuery({
    queryKey: ['task', taskId],
    queryFn: async () => {
      try {
        // Get basic task data first
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*')
          .eq('id', taskId)
          .single();

        if (taskError) throw taskError;
        if (!taskData) return null;

        // Get related data separately
        const [projectData, milestoneData, assignedProfile, creatorProfile] = await Promise.all([
          // Get project data
          taskData.project_id ?
            supabase.from('projects').select('name, project_id').eq('id', taskData.project_id).single() :
            Promise.resolve({ data: null, error: null }),

          // Get milestone data
          taskData.milestone_id ?
            supabase.from('milestones').select('name').eq('id', taskData.milestone_id).single() :
            Promise.resolve({ data: null, error: null }),

          // Get assigned user profile
          taskData.assigned_to ?
            supabase.from('profiles').select('first_name, last_name, email').eq('user_id', taskData.assigned_to).single() :
            Promise.resolve({ data: null, error: null }),

          // Get creator profile
          taskData.created_by ?
            supabase.from('profiles').select('first_name, last_name, email').eq('user_id', taskData.created_by).single() :
            Promise.resolve({ data: null, error: null })
        ]);

        // Combine all data
        const combinedData = {
          ...taskData,
          projects: projectData.data,
          milestones: milestoneData.data,
          profiles: assignedProfile.data,
          creator: creatorProfile.data
        };

        return combinedData;
      } catch (error) {
        console.error('Task query failed:', error);
        return null;
      }
    },
  });



  // Fetch task comments with manual joins
  const { data: comments = [] } = useQuery({
    queryKey: ['task-comments', taskId],
    queryFn: async () => {
      try {
        // Get comments first
        const { data: commentsData, error: commentsError } = await supabase
          .from('task_comments')
          .select('*')
          .eq('task_id', taskId)
          .order('created_at', { ascending: true });

        if (commentsError) throw commentsError;
        if (!commentsData || commentsData.length === 0) return [];

        // Get user profiles for comments
        const userIds = [...new Set(commentsData.map(c => c.user_id).filter(Boolean))];
        if (userIds.length === 0) return commentsData;

        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('user_id, first_name, last_name, email')
          .in('user_id', userIds);

        if (profilesError) {
          console.warn('Profiles query failed:', profilesError);
          return commentsData;
        }

        // Combine comments with profiles
        const commentsWithProfiles = commentsData.map(comment => {
          const profile = profiles?.find(p => p.user_id === comment.user_id);
          return {
            ...comment,
            profiles: profile
          };
        });

        return commentsWithProfiles;
      } catch (error) {
        console.warn('Comments query failed:', error);
        return [];
      }
    },
  });

  // Fetch subtasks with manual joins
  const { data: subtasks = [], refetch: refetchSubtasks } = useQuery({
    queryKey: ['subtasks', taskId],
    queryFn: async () => {
      try {
        // Get subtasks first
        const { data: subtasksData, error: subtasksError } = await supabase
          .from('tasks')
          .select('*')
          .eq('parent_task_id', taskId)
          .order('created_at', { ascending: true });

        if (subtasksError) throw subtasksError;
        if (!subtasksData || subtasksData.length === 0) return [];

        // Get assigned user profiles for subtasks
        const assignedUserIds = [...new Set(subtasksData.map(t => t.assigned_to).filter(Boolean))];
        let profiles = [];

        if (assignedUserIds.length > 0) {
          const { data: profilesData, error: profilesError } = await supabase
            .from('profiles')
            .select('user_id, first_name, last_name, email')
            .in('user_id', assignedUserIds);

          if (!profilesError) {
            profiles = profilesData || [];
          }
        }

        // Combine subtasks with profiles
        const subtasksWithProfiles = subtasksData.map(subtask => {
          const profile = profiles.find(p => p.user_id === subtask.assigned_to);
          return {
            ...subtask,
            profiles: profile
          };
        });

        return subtasksWithProfiles;
      } catch (error) {
        console.warn('Subtasks query failed:', error);
        return [];
      }
    },
  });

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-3/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
          <div className="h-20 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="p-6 text-center">
        <h3 className="text-lg font-medium mb-2">Task Not Found</h3>
        <p className="text-muted-foreground">The task you're looking for doesn't exist.</p>
      </div>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500 text-white';
      case 'in_progress': return 'bg-blue-500 text-white';
      case 'review': return 'bg-purple-500 text-white';
      case 'blocked': return 'bg-red-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const completedSubtasks = subtasks.filter(st => st.status === 'completed').length;
  const subtaskProgress = subtasks.length > 0 ? (completedSubtasks / subtasks.length) * 100 : 0;

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="font-mono">
              {task.task_id}
            </Badge>
            <Badge className={getPriorityColor(task.priority)}>
              {task.priority}
            </Badge>
            <Badge className={getStatusColor(task.status)}>
              {task.status.replace('_', ' ')}
            </Badge>
          </div>
          <h1 className="text-2xl font-bold">{task.title}</h1>
          <p className="text-muted-foreground">
            in {task.projects?.name} ({task.projects?.project_id})
            {task.milestones && ` • ${task.milestones.name}`}
          </p>
        </div>
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        )}
      </div>

      {/* Task Info Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Assigned To</span>
            </div>
            {task.profiles ? (
              <div className="flex items-center gap-2 mt-2">
                <Avatar className="w-6 h-6">
                  <AvatarFallback className="text-xs">
                    {getInitials(task.profiles.first_name, task.profiles.last_name)}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm">
                  {task.profiles.first_name} {task.profiles.last_name}
                </span>
              </div>
            ) : (
              <span className="text-sm text-muted-foreground">Unassigned</span>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Due Date</span>
            </div>
            <p className="text-sm mt-2">
              {task.due_date ? format(new Date(task.due_date), 'MMM d, yyyy') : 'No due date'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Timer className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Time Tracking</span>
            </div>
            <p className="text-sm mt-2">
              {task.actual_hours || 0}h / {task.estimated_hours || 0}h
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Progress</span>
            </div>
            <div className="mt-2">
              <Progress value={subtaskProgress} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">
                {completedSubtasks}/{subtasks.length} subtasks
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Description */}
      {task.description && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Description</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="whitespace-pre-wrap text-sm">
              {task.description}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Subtasks */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <ListTodo className="w-5 h-5" />
              Subtasks ({subtasks.length})
            </CardTitle>
            <Button
              onClick={() => setIsSubtaskSheetOpen(true)}
              className="gap-2"
              size="sm"
            >
              <Plus className="w-4 h-4" />
              Add Subtask
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {subtasks.length > 0 ? (
            <div className="space-y-3">
              {subtasks.map((subtask: any) => (
                <div key={subtask.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className={cn(
                    "w-4 h-4 rounded border-2 flex items-center justify-center",
                    subtask.status === 'completed' ? "bg-green-500 border-green-500" : "border-muted-foreground"
                  )}>
                    {subtask.status === 'completed' && <CheckCircle2 className="w-3 h-3 text-white" />}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className={cn(
                        "font-medium",
                        subtask.status === 'completed' && "line-through text-muted-foreground"
                      )}>
                        {subtask.title}
                      </span>
                      <Badge variant="outline" className="text-xs font-mono">
                        {subtask.task_id}
                      </Badge>
                      <Badge className={cn("text-xs", getPriorityColor(subtask.priority))}>
                        {subtask.priority}
                      </Badge>
                    </div>
                    {subtask.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {subtask.description}
                      </p>
                    )}
                  </div>
                  {subtask.profiles && (
                    <Avatar className="w-6 h-6">
                      <AvatarFallback className="text-xs">
                        {getInitials(subtask.profiles.first_name, subtask.profiles.last_name)}
                      </AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <ListTodo className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p className="text-sm">No subtasks yet</p>
              <p className="text-xs">Break this task down into smaller pieces</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Comments Section */}
      <div className="flex items-center gap-2 mb-4">
        <Button
          variant={showComments ? "default" : "outline"}
          onClick={() => setShowComments(!showComments)}
          className="gap-2"
        >
          <MessageCircle className="w-4 h-4" />
          {showComments ? 'Hide Comments' : `Show Comments (${comments.length})`}
        </Button>
      </div>

      {showComments && (
        <TaskComments
          taskId={taskId}
        />
      )}

      {/* Subtask Creation Sheet */}
      <SubtaskCreateSheet
        open={isSubtaskSheetOpen}
        onOpenChange={setIsSubtaskSheetOpen}
        parentTaskId={taskId}
        projectId={task?.project_id || ''}
      />
    </div>
  );
};