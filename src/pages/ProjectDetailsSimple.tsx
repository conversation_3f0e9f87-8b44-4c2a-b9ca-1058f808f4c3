import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { supabase } from '@/integrations/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { Calendar, DollarSign, Edit } from 'lucide-react';
import { useState } from 'react';
import { useParams } from 'react-router-dom';

const ProjectDetailsSimple = () => {
  const { id } = useParams();
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);

  const { data: project, isLoading } = useQuery({
    queryKey: ['project', id],
    queryFn: async () => {
      try {
        const { data, error } = await (supabase as any)
          .from('projects')
          .select('*')
          .eq('id', id)
          .single();

        if (error) throw error;
        return data;
      } catch (error) {
        console.warn('Project query failed:', error);
        return null;
      }
    },
    enabled: !!id,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planning': return 'bg-blue-500/10 text-blue-600 border-blue-200';
      case 'active': return 'bg-green-500/10 text-green-600 border-green-200';
      case 'on_hold': return 'bg-yellow-500/10 text-yellow-600 border-yellow-200';
      case 'completed': return 'bg-emerald-500/10 text-emerald-600 border-emerald-200';
      case 'cancelled': return 'bg-red-500/10 text-red-600 border-red-200';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-32 w-full" />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-24" />
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!project) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-semibold">Project not found</h2>
          <p className="text-muted-foreground mt-2">
            The project you're looking for doesn't exist or you don't have access to it.
          </p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold tracking-tight">{project.name}</h1>
              <Badge variant="outline" className={getStatusColor(project.status)}>
                {project.status?.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span className="flex items-center gap-1">
                <span className="font-medium text-foreground">{project.project_id}</span>
              </span>
              <span>Created {new Date(project.created_at).toLocaleDateString()}</span>
            </div>
            {project.description && (
              <p className="text-muted-foreground max-w-2xl">{project.description}</p>
            )}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            className="gap-2"
            onClick={() => setIsEditSheetOpen(true)}
          >
            <Edit className="w-4 h-4" />
            Edit Project
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Timeline</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-semibold">
                {project.start_date ? new Date(project.start_date).toLocaleDateString() : 'Not set'}
              </div>
              <div className="text-xs text-muted-foreground">
                to {project.end_date ? new Date(project.end_date).toLocaleDateString() : 'Not set'}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Budget</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-semibold">
                {project.budget ? `$${Number(project.budget).toLocaleString()}` : 'Not set'}
              </div>
              <div className="text-xs text-muted-foreground">
                {project.currency || 'USD'}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Billing</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg font-semibold">
                {project.is_billable ? 'Billable' : 'Non-billable'}
              </div>
              <div className="text-xs text-muted-foreground">
                {project.hourly_rate ? `$${project.hourly_rate}/hr` : 'Fixed rate'}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Content Placeholder */}
        <Card>
          <CardHeader>
            <CardTitle>Project Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-muted-foreground">
              <p>Full project management interface will be available once Supabase types are regenerated.</p>
              <p className="text-sm mt-2">This includes task management, milestones, team collaboration, and file sharing.</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Project Edit Sheet */}
      {project && (
        <ProjectCreateSheet
          open={isEditSheetOpen}
          onOpenChange={setIsEditSheetOpen}
          onProjectCreated={() => {
            // Refresh project data
            window.location.reload();
          }}
          editingProject={project}
        />
      )}
    </DashboardLayout>
  );
};

export default ProjectDetailsSimple;