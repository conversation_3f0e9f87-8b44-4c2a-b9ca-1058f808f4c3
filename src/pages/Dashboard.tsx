import { useAuth } from '@/components/auth/AuthContext';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { ProjectCreateSheet } from '@/components/projects/ProjectCreateSheet';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useQuery } from '@tanstack/react-query';
import {
    Calendar,
    CheckCircle,
    Clock,
    FolderOpen,
    MessageSquare,
    TrendingUp,
    Users
} from 'lucide-react';
import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

const Dashboard = () => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const [isProjectSheetOpen, setIsProjectSheetOpen] = useState(false);

  const handleProjectCreated = () => {
    setIsProjectSheetOpen(false);
    navigate('/projects');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-6">
          <div className="space-y-6">
            <div className="h-8 w-64 bg-muted animate-pulse rounded" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="h-32 bg-muted animate-pulse rounded" />
              <div className="h-32 bg-muted animate-pulse rounded" />
              <div className="h-32 bg-muted animate-pulse rounded" />
            </div>
            <div className="h-64 bg-muted animate-pulse rounded" />
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-muted-foreground mb-4">Please sign in to access the dashboard</p>
          <Button asChild>
            <Link to="/auth">Sign In</Link>
          </Button>
        </div>
      </div>
    );
  }

  // Fetch real dashboard data
  const { data: dashboardData } = useQuery({
    queryKey: ['dashboard-data'],
    queryFn: async () => {
      try {
        // Get projects with stats
        const { data: projects } = await (supabase as any)
          .from('projects')
          .select(`
            *,
            tasks(count),
            team_members:teams!inner(team_members(count))
          `)
          .order('created_at', { ascending: false })
          .limit(3);

        // Get active projects count
        const { count: activeProjectsCount } = await (supabase as any)
          .from('projects')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'active');

        // Get total completed tasks
        const { count: completedTasksCount } = await (supabase as any)
          .from('tasks')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'completed');

        // Get time entries for this month
        const startOfMonth = new Date();
        startOfMonth.setDate(1);
        startOfMonth.setHours(0, 0, 0, 0);
        
        const { data: timeEntries } = await (supabase as any)
          .from('time_entries')
          .select('duration_minutes')
          .gte('created_at', startOfMonth.toISOString());

        const totalHours = timeEntries?.reduce((sum: number, entry: any) => 
          sum + (entry.duration_minutes || 0), 0) / 60 || 0;

        // Get team members count
        const { count: teamMembersCount } = await (supabase as any)
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .eq('is_active', true);

        // Get recent activity from activity logs
        // Note: Using separate queries due to empty activity_logs table
        const { data: activities } = await (supabase as any)
          .from('activity_logs')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(3);

        // Get user profiles for activities if any exist
        const activitiesWithProfiles = await Promise.all(
          (activities || []).map(async (activity: any) => {
            let userProfile = null;
            if (activity.user_id) {
              const { data: profile } = await supabase
                .from('profiles')
                .select('first_name, last_name')
                .eq('user_id', activity.user_id)
                .single();
              userProfile = profile;
            }
            return { ...activity, profiles: userProfile };
          })
        );

        return {
          stats: {
            activeProjects: activeProjectsCount || 0,
            completedTasks: completedTasksCount || 0,
            hoursLogged: Math.round(totalHours),
            teamMembers: teamMembersCount || 0
          },
          recentProjects: projects || [],
          recentActivities: activitiesWithProfiles || []
        };
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        return {
          stats: { activeProjects: 0, completedTasks: 0, hoursLogged: 0, teamMembers: 0 },
          recentProjects: [],
          recentActivities: []
        };
      }
    },
    refetchInterval: 30000,
  });

  const stats = [
    {
      title: "Active Projects",
      value: dashboardData?.stats.activeProjects.toString() || "0",
      description: "Currently in progress",
      icon: FolderOpen,
      color: "text-primary"
    },
    {
      title: "Completed Tasks", 
      value: dashboardData?.stats.completedTasks.toString() || "0",
      description: "Total completed",
      icon: CheckCircle,
      color: "text-success"
    },
    {
      title: "Hours Logged",
      value: dashboardData?.stats.hoursLogged.toString() || "0",
      description: "This month",
      icon: Clock,
      color: "text-warning"
    },
    {
      title: "Team Members",
      value: dashboardData?.stats.teamMembers.toString() || "0",
      description: "Active users",
      icon: Users,
      color: "text-primary"
    }
  ];

  const recentProjects = dashboardData?.recentProjects || [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-primary";
      case "completed": return "bg-success";
      case "on_hold": return "bg-warning";
      case "planning": return "bg-secondary";
      default: return "bg-muted";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "active": return "Active";
      case "completed": return "Completed";
      case "on_hold": return "On Hold";
      case "planning": return "Planning";
      default: return status;
    }
  };

  return (
    <DashboardLayout>
      {/* Header */}
      <div className="border-b bg-gradient-secondary rounded-lg mb-8">
        <div className="px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                Welcome back, {user?.user_metadata?.first_name || 'User'}!
              </h1>
              <p className="text-muted-foreground mt-1">
                Here's what's happening with your projects today.
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Calendar className="mr-2 h-4 w-4" />
                Schedule
              </Button>
              <Button 
                variant="default" 
                size="sm"
                onClick={() => setIsProjectSheetOpen(true)}
              >
                <FolderOpen className="mr-2 h-4 w-4" />
                New Project
              </Button>
            </div>
          </div>
        </div>
      </div>
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index} className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Projects */}
          <Card className="lg:col-span-2 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5" />
                Recent Projects
              </CardTitle>
              <CardDescription>
                Your most active projects and their current status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentProjects.map((project) => (
                <div key={project.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <Badge variant="outline" className="font-mono text-xs">
                        {project.project_id}
                      </Badge>
                      <h3 className="font-semibold">{project.name}</h3>
                      <Badge className={getStatusColor(project.status)}>
                        {getStatusLabel(project.status)}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        Created {new Date(project.created_at).toLocaleDateString()}
                      </span>
                      <span className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {project.team_members?.[0]?.team_members?.[0]?.count || 0} members
                      </span>
                    </div>
                    <div className="mt-3">
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span>Tasks Progress</span>
                        <span>{project.tasks?.[0]?.count || 0} tasks</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all"
                          style={{ width: `${Math.min((project.tasks?.[0]?.count || 0) * 10, 100)}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Quick Actions
              </CardTitle>
              <CardDescription>
                Common tasks and shortcuts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full justify-start" 
                size="sm"
                onClick={() => setIsProjectSheetOpen(true)}
              >
                <FolderOpen className="mr-2 h-4 w-4" />
                Create New Project
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start" 
                size="sm"
                asChild
              >
                <Link to="/projects">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Add Task
                </Link>
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start" 
                size="sm"
                asChild
              >
                <Link to="/time-tracking">
                  <Clock className="mr-2 h-4 w-4" />
                  Log Time
                </Link>
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start" 
                size="sm"
                asChild
              >
                <Link to="/tickets">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Support Ticket
                </Link>
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start" 
                size="sm"
                asChild
              >
                <Link to="/teams">
                  <Users className="mr-2 h-4 w-4" />
                  Invite Team Member
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="mt-8 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest updates from your projects and team
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData?.recentActivities?.length > 0 ? (
                dashboardData.recentActivities.map((activity: any, index: number) => (
                  <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-accent/30">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <span className="text-xs font-semibold text-primary">
                        {activity.profiles?.first_name?.[0] || 'U'}
                        {activity.profiles?.last_name?.[0] || ''}
                      </span>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm">
                        <span className="font-medium">
                          {activity.profiles?.first_name} {activity.profiles?.last_name}
                        </span>
                        {' '}{activity.action}{' '}
                        <span className="font-medium">{activity.entity_type}</span>
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(activity.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No recent activity found</p>
                  <p className="text-xs">Start working on projects to see activity here</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

      <ProjectCreateSheet
        open={isProjectSheetOpen}
        onOpenChange={setIsProjectSheetOpen}
        onProjectCreated={handleProjectCreated}
      />
    </DashboardLayout>
  );
};

export default Dashboard;